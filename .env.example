# ===========================================
# MONOREPO ROOT - Development Infrastructure
# ===========================================
# This file contains shared infrastructure settings for Docker Compose
# Individual services have their own .env files

# TDD Guard Configuration
MODEL_TYPE=claude_cli
USE_SYSTEM_CLAUDE=true
LINTER_TYPE=eslint

# Docker Compose Database Settings (for container configuration)
# These are used by docker-compose.yml to configure containers
# Services connect using their own DATABASE_URL in their respective .env files
POSTGRES_DB=forecastify_dev
POSTGRES_USER=default
POSTGRES_PASSWORD=secret

