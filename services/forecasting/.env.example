# Database (same as Shopify app - shared database)
DATABASE_URL=postgresql://default:secret@localhost:5432/forecastify_dev

# Redis (for caching)
REDIS_URL=redis://localhost:6379

# Vector Database (for document search)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=""

# IBM watsonx.ai (forecasting AI)
WATSONX_API_KEY=your-watsonx-api-key-here
WATSONX_PROJECT_ID=your-project-id-here
WATSONX_INSTANCE_URL=https://us-south.ml.cloud.ibm.com