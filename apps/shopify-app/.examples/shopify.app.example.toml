# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "your_client_id_here"
name = "your_app_name"
handle = "your-app-handle"
application_url = "https://localhost:3000"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "your-dev-store.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

[[webhooks.subscriptions]]
uri = "/webhooks"
compliance_topics = [
  "customers/data_request",
  "customers/redact",
  "shop/redact",
]

[[webhooks.subscriptions]]
topics = ["app_subscriptions/approaching_capped_amount"]
uri = "/webhooks/app/approaching_capped_amount"

[[webhooks.subscriptions]]
topics = ["app_purchases_one_time/update"]
uri = "/webhooks/app/one_time_purchase_updated"

[[webhooks.subscriptions]]
topics = ["app/scopes_update"]
uri = "/webhooks/app/scopes_update"

[[webhooks.subscriptions]]
topics = ["app_subscriptions/update"]
uri = "/webhooks/app/subscriptions_update"

[[webhooks.subscriptions]]
topics = ["app/uninstalled"]
uri = "/webhooks/app/uninstalled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products, write_discounts, write_discounts_allocator_functions, read_products,customer_read_customers,customer_read_orders,customer_read_store_credit_account_transactions,customer_read_store_credit_accounts,unauthenticated_read_product_listings"

[auth]
redirect_urls = [
  "https://localhost:3000/auth/callback",
  "https://localhost:3000/auth/shopify/callback",
  "https://localhost:3000/api/auth/callback",
]

[pos]
embedded = false

[mcp.customer_authentication]
redirect_uris = ["https://your-domain.com/callback"]
