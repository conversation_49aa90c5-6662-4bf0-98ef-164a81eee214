{"name": "app", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "format": "prettier --write .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:fresh": "tsx ./app/utils/commands/fresh.ts", "db:seed:dev": "tsx ./app/utils/commands/seed.dev.ts", "db:seed:prod": "tsx ./app/utils/commands/seed.prod.ts", "db:faker": "tsx ./app/utils/commands/faker.ts", "db:status": "tsx ./app/utils/commands/db-status.ts", "db:unused": "./.scripts/unused.sh", "postinstall": "pnpm run db:generate"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@prisma/client": "^6.8.2", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@shopify/app-bridge-react": "^4.1.6", "@shopify/cli": "^3.63.1", "@shopify/polaris": "^12.0.0", "@shopify/polaris-icons": "9.3.1", "@shopify/polaris-viz": "^16.16.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "bullmq": "^5.53.2", "dayjs": "^1.11.13", "node-fetch": "^3.3.2", "ioredis": "^5.6.1", "isbot": "^5.1.0", "langchain": "^0.3.29", "lucide-react": "^0.511.0", "openai": "^5.8.1", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "prisma": "^6.8.2", "react": "^18.2.0", "react-animated-rating": "^1.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "remix-utils": "^8.7.0", "swiper": "^11.2.8", "vite-tsconfig-paths": "^5.0.1", "winston": "^3.17.0", "winston-slack-webhook-transport": "^2.3.6", "@langchain/textsplitters": "^0.1.0", "@qdrant/js-client-rest": "^1.14.1", "file-type": "^21.0.0", "lodash": "^4.17.21", "mammoth": "^1.9.1", "mime-types": "^3.0.1", "pdf-parse": "^1.1.1", "tiktoken": "^1.0.21", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@faker-js/faker": "^9.2.0", "@flydotio/dockerfile": "^0.7.10", "@remix-run/eslint-config": "^2.16.1", "@remix-run/route-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/ioredis": "^5.0.0", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "prettier": "^3.2.4", "@types/uuid": "^10.0.0", "tsx": "^4.19.4", "typescript": "^5.2.2", "vite": "^6.2.2"}, "workspaces": {"packages": ["extensions/*"]}, "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}, "overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}}