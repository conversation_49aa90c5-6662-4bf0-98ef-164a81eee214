# ===========================================
# SHOPIFY APP ENVIRONMENT VARIABLES
# ===========================================

# Database
DATABASE_URL=postgres://username:password@localhost:5432/forecastify_dev?schema=public

# Slack (Enable this if you want to receive logs in slack)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK_URL

# Redis
REDIS_URL=redis://localhost:6379

# OpenAI
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Qdrant Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=""  # Optional for local dev

# App Configuration
NODE_ENV="development"
MAX_FILE_SIZE=52428800
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
BATCH_SIZE=50

# Collection names
VECTOR_COLLECTION=documents

# File upload
UPLOAD_DIR=./uploads

# AWS Configuration
S3_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID
S3_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY
S3_REGION=us-east-1
S3_BUCKET=your-s3-bucket-name
S3_USE_PATH_STYLE_ENDPOINT=false
S3_URL=https://s3.your-region.amazonaws.com/your-bucket-name

# IBM watsonx.ai
WATSONX_API_KEY=your-watsonx-api-key-here
WATSONX_PROJECT_ID=your-project-id-here
WATSONX_INSTANCE_URL=https://us-south.ml.cloud.ibm.com