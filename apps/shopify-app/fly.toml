# fly.toml app configuration file generated for remix-delicate-rain-1682 on 2025-06-16T17:47:31+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'remix-delicate-rain-1682'
primary_region = 'cdg'

[build]

[deploy]
  release_command = 'npx prisma migrate deploy'

[env]
  PORT = '3000'
  SCOPES = 'read_products,write_discounts,write_discounts_allocator_functions,write_products'
  SHOPIFY_API_KEY = '10ea6276609bbba1e1813a7f8c7f09b4'
  SHOPIFY_APP_URL = 'https://remix-delicate-rain-1682.fly.dev'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
