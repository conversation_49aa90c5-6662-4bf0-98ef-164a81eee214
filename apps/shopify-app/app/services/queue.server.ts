import { queueManager } from "app/lib/queue-manager.server";
import { JobTypes } from "app/types/queue";

export class QueueService {
  static async sendEmail(emailData: JobTypes["send-email"]) {
    return await queueManager.addJob("send-email", emailData);
  }

  static async processImage(imageData: JobTypes["process-image"]) {
    return await queueManager.addJob("process-image", imageData, {
      priority: 10, // Higher priority for image processing
    });
  }

  static async processDocument(documentData: JobTypes["process-document"]) {
    return await queueManager.addJob("process-document", documentData, {
      priority: 5, // Medium priority for document processing
    });
  }

  static async scheduleReport(
    reportData: JobTypes["generate-report"],
    cron: string,
  ) {
    return await queueManager.addJob("generate-report", reportData, {
      repeat: { cron },
    });
  }

  static async delayedEmail(
    emailData: JobTypes["send-email"],
    delayMs: number,
  ) {
    return await queueManager.addJob("send-email", emailData, {
      delay: delayMs,
    });
  }

  static async delayedDocument(
    documentData: JobTypes["process-document"],
    delayMs: number,
  ) {
    return await queueManager.addJob("process-document", documentData, {
      delay: delayMs,
    });
  }
}

// Export the missing functions for backward compatibility
export async function addDocumentToQueue(data: JobTypes["process-document"]) {
  const job = await QueueService.processDocument(data);
  return job.id;
}

export async function getQueueStats() {
  const documentQueue = queueManager.getQueue("process-document");

  const [waiting, active, completed, failed] = await Promise.all([
    documentQueue.getWaiting(),
    documentQueue.getActive(),
    documentQueue.getCompleted(),
    documentQueue.getFailed(),
  ]);

  return {
    waiting: waiting.length,
    active: active.length,
    completed: completed.length,
    failed: failed.length,
  };
}
