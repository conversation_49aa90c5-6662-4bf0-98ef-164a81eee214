import winston from "winston";
import <PERSON>lack<PERSON>ook from "winston-slack-webhook-transport";

const enumerateErrorFormat = winston.format((info) => {
  if (info instanceof Error) {
    Object.assign(info, { message: info.stack });
  }
  return info;
});

const logger = winston.createLogger({
  level: process.env.NODE_ENV === "development" ? "debug" : "info",
  format: winston.format.combine(
    enumerateErrorFormat(),
    process.env.NODE_ENV === "development"
      ? winston.format.colorize()
      : winston.format.uncolorize(),
    winston.format.splat(),
    winston.format.printf(({ level, message }) => `${level}: ${message}`),
  ),
  transports: [
    new winston.transports.Console({
      stderrLevels: ["error"],
    }),
    new SlackHook({
      webhookUrl: process.env.SLACK_WEBHOOK_URL || "",
      level: "error", // Only send error level messages to Slack
      formatter: (info) => ({
        text: `[${info.timestamp}] ${info.level.toUpperCase()}: ${info.message}`,
      }),
    }),
    new winston.transports.File({ filename: "logs/error.log", level: "error" }),
    new winston.transports.File({ filename: "logs/combined.log" }),
  ],
});

export default logger;
