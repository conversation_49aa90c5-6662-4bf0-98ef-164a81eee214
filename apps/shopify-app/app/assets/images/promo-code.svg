
<svg width="170" height="170" viewBox="0 0 170 170" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="170" height="170" fill="#1E1E1E"/>
<g id="Promotion Code">
<path d="M-2070 -456C-2070 -457.105 -2069.1 -458 -2068 -458H2869C2870.1 -458 2871 -457.105 2871 -456V3826C2871 3827.1 2870.1 3828 2869 3828H-2068C-2069.1 3828 -2070 3827.1 -2070 3826V-456Z" fill="#404040"/>
<path d="M-2068 -457H2869V-459H-2068V-457ZM2870 -456V3826H2872V-456H2870ZM2869 3827H-2068V3829H2869V3827ZM-2069 3826V-456H-2071V3826H-2069ZM-2068 3827C-2068.55 3827 -2069 3826.55 -2069 3826H-2071C-2071 3827.66 -2069.66 3829 -2068 3829V3827ZM2870 3826C2870 3826.55 2869.55 3827 2869 3827V3829C2870.66 3829 2872 3827.66 2872 3826H2870ZM2869 -457C2869.55 -457 2870 -456.552 2870 -456H2872C2872 -457.657 2870.66 -459 2869 -459V-457ZM-2068 -459C-2069.66 -459 -2071 -457.657 -2071 -456H-2069C-2069 -456.552 -2068.55 -457 -2068 -457V-459Z" fill="white" fill-opacity="0.1"/>
<g id="Default">
<path d="M-1883 -216C-1883 -217.105 -1882.1 -218 -1881 -218H316C317.104 -218 318 -217.105 318 -216V357C318 358.105 317.104 359 316 359H-1881C-1882.1 359 -1883 358.105 -1883 357V-216Z" fill="#5E5D5D"/>
<path d="M-1881 -217H316V-219H-1881V-217ZM317 -216V357H319V-216H317ZM316 358H-1881V360H316V358ZM-1882 357V-216H-1884V357H-1882ZM-1881 358C-1881.55 358 -1882 357.552 -1882 357H-1884C-1884 358.657 -1882.66 360 -1881 360V358ZM317 357C317 357.552 316.552 358 316 358V360C317.657 360 319 358.657 319 357H317ZM316 -217C316.552 -217 317 -216.552 317 -216H319C319 -217.657 317.657 -219 316 -219V-217ZM-1881 -219C-1882.66 -219 -1884 -217.657 -1884 -216H-1882C-1882 -216.552 -1881.55 -217 -1881 -217V-219Z" fill="white" fill-opacity="0.1"/>
<g id="Modal - In processing" filter="url(#filter0_diiii_1154_15851)">
<path d="M-434 -48C-434 -59.0457 -425.046 -68 -414 -68H166C177.046 -68 186 -59.0457 186 -48V226C186 237.046 177.046 246 166 246H-414C-425.046 246 -434 237.046 -434 226V-48Z" fill="white"/>
<g id="Content">
<g id="Frame 1000008246">
<g id="Frame">
<g id="Group 1000008223">
<path id="Vector" d="M85.4436 150.784C121.775 150.784 151.228 121.331 151.228 84.9992C151.228 48.6667 121.775 19.2148 85.4436 19.2148C49.111 19.2148 19.6592 48.6667 19.6592 84.9992C19.6592 121.331 49.111 150.784 85.4436 150.784Z" fill="#F0F1F2"/>
<path id="Vector_2" d="M72.1094 97.4453L95.934 12.459C96.6452 9.96985 98.3342 7.9252 100.557 6.76953L93.4449 81.0881L77.4432 99.2233L72.1094 97.4453Z" fill="#828A91"/>
<path id="Vector_3" d="M80.8209 101.624C81.2654 100.201 80.732 98.6901 79.4874 97.7123L74.7758 94.6008C74.1535 94.1563 73.3535 93.8896 72.6423 93.8896C71.2199 93.8896 69.8864 94.7786 69.3531 96.0232L60.1966 117.003C58.6853 120.47 56.1073 123.226 52.8181 125.004C51.6624 125.626 50.3289 126.071 49.0843 126.426C41.4391 128.471 35.9275 135.849 36.7276 144.295C37.4387 152.029 43.5727 158.429 51.3068 159.407C58.5964 160.296 65.1749 156.563 68.2863 150.784L71.9311 145.095C73.1757 143.139 73.0868 140.739 71.8422 138.783C70.8643 137.361 70.5976 135.672 71.131 133.983L80.8209 101.624ZM53.4403 154.34C47.0397 154.34 41.8836 149.184 41.8836 142.783C41.8836 136.383 47.0397 131.227 53.4403 131.227C59.841 131.227 64.9971 136.383 64.9971 142.783C64.9971 149.184 59.841 154.34 53.4403 154.34Z" fill="#399C97"/>
<path id="Vector_4" d="M111.224 19.2148H13.4365V74.3315H111.224V19.2148Z" fill="#E9AF58"/>
<path id="Vector_5" d="M104.113 26.3281H20.5488V67.2211H104.113V26.3281Z" fill="#D9942D"/>
<path id="Vector_6" d="M94.7789 34.3289H92.112V37.8848H94.7789V34.3289ZM94.7789 41.4408H92.112V44.9967H94.7789V41.4408ZM94.7789 48.5526H92.112V52.1085H94.7789V48.5526ZM94.7789 55.6644H92.112V59.2203H94.7789V55.6644ZM94.7789 26.3281H92.112V30.773H94.7789V26.3281ZM94.7789 62.7762H92.112V67.2211H94.7789V62.7762ZM52.908 39.1294C53.6192 38.4182 53.6192 37.3515 52.908 36.6403C52.1968 35.9291 51.1301 35.9291 50.4189 36.6403L32.6393 54.4198C31.9281 55.131 31.9281 56.1978 32.6393 56.909C33.3505 57.6202 34.4173 57.6202 35.1285 56.909L52.908 39.1294ZM35.6618 44.5522C32.9949 44.5522 30.7725 42.3297 30.7725 39.6628C30.7725 36.9959 32.9949 34.7734 35.6618 34.7734C38.3288 34.7734 40.5512 36.9959 40.5512 39.6628C40.5512 42.3297 38.3288 44.5522 35.6618 44.5522ZM35.6618 37.4404C34.4173 37.4404 33.4394 38.4182 33.4394 39.6628C33.4394 40.9074 34.4173 41.8852 35.6618 41.8852C36.9064 41.8852 37.8843 40.9074 37.8843 39.6628C37.8843 38.4182 36.9064 37.4404 35.6618 37.4404ZM49.8855 58.7758C47.2186 58.7758 44.9961 56.5534 44.9961 53.8865C44.9961 51.2195 47.2186 48.9971 49.8855 48.9971C52.5524 48.9971 54.7749 51.2195 54.7749 53.8865C54.7749 56.5534 52.5524 58.7758 49.8855 58.7758ZM49.8855 51.664C48.6409 51.664 47.663 52.6419 47.663 53.8865C47.663 55.131 48.6409 56.1089 49.8855 56.1089C51.1301 56.1089 52.1079 55.131 52.1079 53.8865C52.1079 52.6419 51.1301 51.664 49.8855 51.664Z" fill="white"/>
<path id="Vector_7" d="M95.2227 97.4453L71.3981 12.459C70.6869 9.96985 68.9978 7.9252 66.7754 6.76953L73.8872 81.0881L89.8888 99.2233L95.2227 97.4453Z" fill="#D2D5D9"/>
<path id="Vector_8" d="M68.7311 15.2148L84.1994 70.5093C84.7328 72.465 86.5107 73.8874 88.5554 73.8874L71.3981 12.5479C70.6869 9.96985 68.9978 7.9252 66.7754 6.76953L67.3088 12.1923C67.9311 13.0813 68.4644 14.1481 68.7311 15.2148Z" fill="#E8E9EB"/>
<path id="Vector_9" d="M82.7773 83.2225C83.4846 83.2225 84.1629 82.9416 84.6631 82.4414C85.1632 81.9413 85.4442 81.2629 85.4442 80.5556C85.4442 79.8483 85.1632 79.1699 84.6631 78.6698C84.1629 78.1696 83.4846 77.8887 82.7773 77.8887C82.07 77.8887 81.3916 78.1696 80.8915 78.6698C80.3913 79.1699 80.1104 79.8483 80.1104 80.5556C80.1104 81.2629 80.3913 81.9413 80.8915 82.4414C81.3916 82.9416 82.07 83.2225 82.7773 83.2225Z" fill="#828A91"/>
<path id="Vector_10" d="M118.336 127.404C117.002 127.048 115.758 126.604 114.602 125.981C111.313 124.204 108.646 121.359 107.223 117.981L98.067 97.0008C97.3558 95.6673 96.1112 94.7783 94.6889 94.7783C93.8888 94.7783 93.1776 95.045 92.5553 95.4895L87.8437 98.6009C86.6881 99.4899 86.1547 101.001 86.5103 102.512L96.289 134.871C96.7335 136.471 96.4668 138.249 95.5779 139.672C94.3333 141.539 94.2444 144.028 95.489 145.983L97.8003 149.628C99.6672 149.273 101.534 148.828 103.312 148.295C102.69 146.872 102.334 145.272 102.334 143.672C102.334 137.271 107.49 132.115 113.891 132.115C118.336 132.115 122.158 134.605 124.114 138.249C125.536 137.183 126.959 136.116 128.292 134.871C126.07 131.226 122.514 128.559 118.336 127.404Z" fill="#108060"/>
</g>
</g>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_diiii_1154_15851" x="-446" y="-68" width="644" height="346" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="8" operator="erode" in="SourceAlpha" result="effect1_dropShadow_1154_15851"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.101961 0 0 0 0 0.101961 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1154_15851"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1154_15851" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0.5 0"/>
<feBlend mode="multiply" in2="shape" result="effect2_innerShadow_1154_15851"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
<feBlend mode="multiply" in2="effect2_innerShadow_1154_15851" result="effect3_innerShadow_1154_15851"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
<feBlend mode="multiply" in2="effect3_innerShadow_1154_15851" result="effect4_innerShadow_1154_15851"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0"/>
<feBlend mode="multiply" in2="effect4_innerShadow_1154_15851" result="effect5_innerShadow_1154_15851"/>
</filter>
</defs>
</svg>
