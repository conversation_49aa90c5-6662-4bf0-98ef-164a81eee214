import {
  <PERSON>s,
  Meta,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
} from "@remix-run/react";

function Footer() {
  return (
    <footer style={{ textAlign: "center", padding: "1rem", color: "#888888" }}>
      © 2025 ShopiFast. All rights reserved.
    </footer>
  );
}

export default function App() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <link rel="preconnect" href="https://cdn.shopify.com/" />
        <link
          rel="stylesheet"
          href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        />
        <Meta />
        <Links />
      </head>
      <body
        style={{
          margin: 0,
          minHeight: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <div style={{ flex: 1 }}>
          <Outlet />
        </div>
        <Footer />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
