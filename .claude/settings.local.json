{"permissions": {"allow": ["Bash(eza:*)", "WebFetch(domain:github.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(tdd-guard:*)", "Bash(npm test)", "Bash(npm run lint)", "<PERSON><PERSON>(uv run:*)", "Bash(npm run lint:*)", "Bash(git add:*)", "WebFetch(domain:shopify.dev)", "WebFetch(domain:docs.anthropic.com)", "mcp__shopify-dev-mcp__search_dev_docs", "mcp__shopify-dev-mcp__introspect_admin_schema", "mcp__shopify-dev-mcp__get_started", "mcp__shopify-dev-mcp__fetch_docs_by_path", "WebFetch(domain:dataplatform.cloud.ibm.com)", "WebFetch(domain:www.ibm.com)", "mcp__linear-server__list_teams", "mcp__linear-server__list_projects", "mcp__linear-server__list_issue_statuses", "mcp__linear-server__list_issue_labels", "mcp__linear-server__create_issue", "Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "WebFetch(domain:www.shopifast.dev)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["shopify-dev-mcp"]}