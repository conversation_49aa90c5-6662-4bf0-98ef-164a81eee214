# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Forecastify is an AI-powered inventory intelligence platform built as a **microservices monorepo**. The platform provides demand forecasting for e-commerce businesses, currently focused on Shopify integration with plans for multi-marketplace expansion.

## Architecture

The project follows a microservices architecture with these key components:

- **apps/shopify-app**: Remix-based Shopify App Bridge application with Polaris UI
- **services/forecasting**: FastAPI-based AI forecasting microservice 
- **packages/**: Shared libraries (future expansion)
- **infrastructure/**: Deployment and DevOps configurations

### Key Technologies
- **Frontend**: Remix + React + Shopify Polaris + TypeScript
- **Backend**: FastAPI + Python (3.12+)
- **Database**: PostgreSQL with Prisma ORM
- **Infrastructure**: Docker Compose, <PERSON><PERSON>, Qdrant (vector DB)
- **Package Management**: pnpm (Node.js), uv (Python)

## Development Setup Commands

### Infrastructure
```bash
# Start all services (PostgreSQL, Redis, Qdrant, etc.)
docker compose up -d

# Check service status
docker compose ps
```

### Shopify App Development
```bash
cd apps/shopify-app

# Start development server with Shopify CLI
pnpm run dev

# Database operations
pnpm run db:migrate          # Run Prisma migrations
pnpm run db:generate         # Generate Prisma client
pnpm run db:push            # Push schema changes
pnpm run db:fresh           # Fresh database setup
pnpm run db:seed:dev        # Seed development data
pnpm run db:faker           # Generate fake data
pnpm run db:status          # Check database status

# Build and formatting
pnpm run build              # Production build
pnpm run lint               # ESLint check
pnpm run format             # Prettier formatting

# Shopify-specific commands
pnpm run deploy             # Deploy to Shopify
pnpm run generate           # Generate Shopify boilerplate
pnpm run config:link        # Link app configuration
```

### Forecasting Service Development
```bash
cd services/forecasting

# Start development server
uv run fastapi dev

# Package management
uv add <package>            # Add Python dependency
uv sync                     # Sync dependencies

# Testing
uv run python -m pytest    # Run tests
```

### Workspace-Level Commands
```bash
# From project root
pnpm install                # Install all Node.js dependencies
pnpm run format             # Format all code across workspace
```

## Database Schema Architecture

The application uses PostgreSQL with Prisma ORM. Key models include:

### Core Entities
- **User**: System users with role-based access
- **Shop**: Shopify store instances with settings and metadata
- **Session**: Shopify authentication sessions

### Billing System
- **Plan**: Flexible pricing with one-time, subscription, and usage-based options
- **OneTimePurchase**: Single purchases (AppPurchaseOneTime)
- **Subscription**: Recurring billing (AppSubscription) 
- **UsageCharge**: Usage-based billing (AppUsageRecord)
- **Coupon/CouponUsage**: Discount system

### Document Management
- **Document**: File uploads with AI processing pipeline
- **DocumentChunk**: Text chunks for vector search
- Integration with S3 storage and Qdrant vector database

### Chat System
- **Conversation/Message**: Customer chat functionality
- **CustomerToken**: OAuth tokens for customer account access

## Service Integration Points

### Shopify App → Forecasting Service
- The Shopify app calls the forecasting service at `http://localhost:8001` (development)
- Authentication handled via internal service tokens
- Data exchange for sales history → forecast predictions

### External Services
- **PostgreSQL**: Primary database (port 5432)
- **Redis**: Session storage and queues (port 6379) 
- **Qdrant**: Vector database for document search (port 6333)
- **S3**: File storage for document uploads

## Development Tools & URLs

When running `docker compose up -d`, these services are available:

- **Shopify App**: http://localhost:3000
- **Forecasting API**: http://localhost:8001
- **Adminer** (DB admin): http://localhost:8000
- **MailDev** (email testing): http://localhost:1080
- **RedisInsight**: http://localhost:5540

## Code Patterns

### Shopify App Patterns
- Routes use flat-file routing (`@remix-run/fs-routes`)
- Authentication via `authenticate.admin()` from Shopify App Remix
- GraphQL queries in `/app/graphql/` directory
- Queue processing with BullMQ and Redis
- Document processing pipeline with AI summarization

### Database Patterns
- All models use UUID primary keys
- Soft deletes via `isActive` flags where applicable
- Audit trails with `createdAt`/`updatedAt` timestamps
- Foreign key relationships with cascading deletes for shop data

### Error Handling
- Winston logging with Slack webhook integration
- Structured error responses in API routes
- Graceful fallbacks for external service failures

## Testing Strategy

### Shopify App
- No test framework currently configured
- Manual testing via Shopify development store

### Forecasting Service  
- pytest for unit testing
- FastAPI test client for API testing

## Required Environment Variables

Key environment variables needed for development:

```bash
# Shopify Configuration
SHOPIFY_API_KEY=
SHOPIFY_API_SECRET=
SHOPIFY_APP_URL=
SCOPES=

# Database
DATABASE_URL=postgresql://default:secret@localhost:5432/forecastify_dev

# External Services
REDIS_URL=redis://localhost:6379
QDRANT_URL=http://localhost:6333
OPENAI_API_KEY=

# AWS S3 (for file uploads)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
AWS_S3_BUCKET=
```

## MCP Integration Notes

This project is designed to work with Claude Code MCPs:
- **Linear MCP**: For project management and issue tracking
- **Shopify MCP**: For accessing Shopify documentation during development
- **TDD-Guard**: Enforces test-driven development practices