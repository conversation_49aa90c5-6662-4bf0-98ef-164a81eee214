# Forecastify - AI-Powered Inventory Intelligence Platform

> **Simple inventory management that thinks ahead**

AI-powered demand forecasting platform designed for growing e-commerce businesses across multiple marketplaces.

## 🎯 Vision

Help merchants never run out of bestsellers while freeing up cash tied up in dead stock - without enterprise complexity.

## 🏗️ Architecture

Forecastify is built as a **microservices monorepo** supporting multiple marketplace integrations:

```
forecastify/
├── apps/                           # Customer-facing applications
│   ├── shopify-app/               # Shopify marketplace app
│   ├── web/                       # Marketing website (forecastify.ai)
│   └── [future: woocommerce-app, magento-app]
├── services/                      # Backend microservices
│   └── forecasting/               # AI forecasting engine (FastAPI)
├── packages/                      # Shared libraries
│   └── [future: ui, utils, types]
└── infrastructure/                # Deployment & DevOps
```

## 🚀 Applications

### Shopify App
**Current marketplace integration**
- Built on Remix + Shopify App Bridge
- Target: Shopify stores (50+ SKUs, 6+ months history)
- Pricing: $79-99/month

### Marketing Website
**Public website (forecastify.ai)**
- Coming soon
- Product information, docs, customer portal

### Future Expansions
- **WooCommerce App**: WordPress e-commerce integration
- **BigCommerce App**: Enterprise e-commerce platform
- **Standalone SaaS**: Direct subscription service

## 🤖 AI Forecasting Engine

**Powered by IBM watsonx Granite TTM**
- FastAPI microservice architecture
- Requires only 60 days of sales data
- Multi-horizon forecasting (30/60/90 days)
- "Traffic light" inventory status system

## 🎯 Market Position

**Our Sweet Spot**: Fabrikatör's beloved simplicity + AI capabilities at competitive pricing

| Feature | Prediko | Fabrikatör | **Forecastify** |
|---------|---------|------------|-----------------|
| AI Forecasting | ✅ Complex | ❌ Basic | ✅ **Simple** |
| Pricing | $119+ | $99+ | **$79-99** |
| Setup Time | Weeks | Minutes | **Minutes** |
| User Experience | Overwhelming | Great | **Great + AI** |

## 💻 Development Setup

### Prerequisites
- **Node.js** (^18.20 || ^20.10 || >=21.0.0)
- **Python** (>=3.12)
- **pnpm** (workspace manager)
- **uv** (Python package manager)
- **Docker & Docker Compose**

### Quick Start

1. **Clone and install:**
   ```bash
   git clone <repository-url>
   cd forecastify
   pnpm install  # Install Node.js dependencies
   ```

2. **Set up environment:**
   ```bash
   cp .env.example .env
   # Configure: DATABASE_URL, REDIS_URL, OPENAI_API_KEY, etc.
   ```

3. **Start infrastructure:**
   ```bash
   docker compose up -d
   ```

4. **Configure Shopify App:**
   ```bash
   cd apps/shopify-app
   cp .env.example .env
   # Configure required environment variables in .env:
   # - DATABASE_URL, REDIS_URL, OPENAI_API_KEY, etc.
   ```

5. **Set up Shopify App with CLI:**
   ```bash
   # Option A: Create new Shopify app (Recommended)
   shopify app config link
   # - Select "create as new app"
   # - Name your app
   # - Choose Partner organization
   # - CLI will auto-configure SHOPIFY_API_KEY and SHOPIFY_API_SECRET
   
   # Option B: Link to existing app
   shopify app config link
   # - Select existing app from your Partner dashboard
   ```

6. **Configure App Permissions:**
   ```bash
   # Edit shopify.app.toml to configure access scopes
   # Current scopes = "" (add required scopes for your app)
   # Example scopes: "write_products,read_customers,write_orders"
   # Only request scopes your app actually needs
   ```

7. **Initialize Database:**
   ```bash
   pnpm run db:migrate            # Run database migrations
   pnpm run db:seed:dev           # Seed development data (plans, coupons, etc.)
   ```

8. **Deploy App Configuration:**
   ```bash
   pnpm run deploy                # Updates Shopify app configuration
   ```

9. **Start services:**
   
   **Shopify App:**
   ```bash
   cd apps/shopify-app
   pnpm run dev                   # Start with Shopify CLI hot reload
   ```
   
   **Forecasting Service:**
   ```bash
   cd services/forecasting
   uv run fastapi dev
   ```

### Development Services
- **Shopify App**: http://localhost:3000
- **Forecasting API**: http://localhost:8000
- **Adminer**: http://localhost:8000 (Database admin)
- **MailDev**: http://localhost:1080 (Email testing)
- **RedisInsight**: http://localhost:5540 (Redis monitoring)

## 🛠️ Common Commands

**Workspace (from root):**
```bash
pnpm install                    # Install all Node.js dependencies
pnpm run format                 # Format all code
docker compose up -d            # Start all infrastructure
```

**Shopify App:**
```bash
cd apps/shopify-app
pnpm run dev                    # Start development server with Shopify CLI
pnpm run build                  # Build for production
pnpm run config:link            # Link to Shopify app configuration
pnpm run deploy                 # Deploy app to Shopify
pnpm run db:migrate             # Run database migrations
pnpm run db:seed:dev            # Seed development data
pnpm run db:fresh               # Fresh database setup
```

**Forecasting Service:**
```bash
cd services/forecasting
uv add <package>                # Add Python dependency
uv run fastapi dev              # Start development server
uv run python -m pytest        # Run tests
```

## 🧰 Developer Tools

**Required MCPs for Claude Code development:**
- **Linear MCP**: Project and issue tracking
- **Shopify MCP**: Shopify documentation access
- **TDD-Guard**: Enforce test-driven development

## 🎯 Target Markets

### Current: Shopify
- Stores managing 50+ SKUs
- 6+ months of sales history
- Growing businesses needing AI insights

### Future Expansion
- **WooCommerce**: WordPress ecosystem
- **BigCommerce**: Enterprise merchants
- **Direct SaaS**: Platform-agnostic solution

## 📄 License

Proprietary - All rights reserved
