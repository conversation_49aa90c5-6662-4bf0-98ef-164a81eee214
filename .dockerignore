node_modules
.DS_Store

/.cache
/build
/app/build
/public/build/
/public/_dev
/app/public/build
/prisma/dev.sqlite
/prisma/dev.sqlite-journal
database.sqlite

.env
.env.local
.env.development
.env.production
.env.test

package-lock.json
yarn.lock
pnpm-lock.yaml

/extensions/*/dist

# Ignore shopify files created during app dev
.shopify/*
.shopify.lock

*.log

# Additional Docker-specific ignores
.git
.gitignore
README.md
.dockerignore
Dockerfile
