services:
  postgres:
    ports:
      - 5432:5432
    environment:
      - POSTGRES_DB=forecastify_dev
      - POSTGRES_USER=default
      - POSTGRES_PASSWORD=secret
    image: postgres:13.3
    volumes:
      - forecastify_postgres_data:/var/lib/postgresql/data

  adminer:
    image: adminer
    restart: always
    ports:
      - "8000:8080"
    depends_on:
      - postgres

  mailtrap:
    image: maildev/maildev
    container_name: forecastify_mailtrap_dev
    ports:
      - "1080:1080"
      - "1025:1025"
    environment:
      - MAILDEV_INCOMING_USER=
      - MAILDEV_INCOMING_PASS=

  redis:
    image: redis:7
    container_name: forecastify_redis_dev
    ports:
      - "6379:6379"
    restart: always

  redisinsight:
    image: redis/redisinsight:latest
    container_name: forecastify_redisinsight_dev
    ports:
      - "5540:5540"
    restart: always
    depends_on:
      - redis

  qdrant:
    image: qdrant/qdrant:v1.7.4
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - forecastify_qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

#  forecasting-service:
#    build: ./services/forecasting
#    container_name: forecastify_forecasting_dev
#    ports:
#      - "8001:8000"
#    environment:
#      - DATABASE_URL=*****************************************/forecastify_dev
#      - REDIS_URL=redis://redis:6379
#      - QDRANT_URL=http://qdrant:6333
#    depends_on:
#      - postgres
#      - redis
#      - qdrant
#    volumes:
#      - ./services/forecasting:/app
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
#      interval: 30s
#      timeout: 10s
#      retries: 3

volumes:
  forecastify_postgres_data:
  forecastify_qdrant_data:
